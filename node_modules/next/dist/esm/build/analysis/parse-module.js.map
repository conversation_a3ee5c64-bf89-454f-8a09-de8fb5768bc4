{"version": 3, "sources": ["../../../src/build/analysis/parse-module.ts"], "sourcesContent": ["import { LRUCache } from '../../server/lib/lru-cache'\nimport { withPromiseCache } from '../../lib/with-promise-cache'\nimport { createHash } from 'crypto'\nimport { parse } from '../swc'\n\n/**\n * Parses a module with SWC using an LRU cache where the parsed module will\n * be indexed by a sha of its content holding up to 500 entries.\n */\nexport const parseModule = withPromiseCache(\n  new LRUCache<any>(500),\n  async (filename: string, content: string) =>\n    parse(content, { isModule: 'unknown', filename }).catch(() => null),\n  (_, content) => createHash('sha1').update(content).digest('hex')\n)\n"], "names": ["L<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createHash", "parse", "parseModule", "filename", "content", "isModule", "catch", "_", "update", "digest"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,6BAA4B;AACrD,SAASC,gBAAgB,QAAQ,+BAA8B;AAC/D,SAASC,UAAU,QAAQ,SAAQ;AACnC,SAASC,KAAK,QAAQ,SAAQ;AAE9B;;;CAGC,GACD,OAAO,MAAMC,cAAcH,iBACzB,IAAID,SAAc,MAClB,OAAOK,UAAkBC,UACvBH,MAAMG,SAAS;QAAEC,UAAU;QAAWF;IAAS,GAAGG,KAAK,CAAC,IAAM,OAChE,CAACC,GAAGH,UAAYJ,WAAW,QAAQQ,MAAM,CAACJ,SAASK,MAAM,CAAC,QAC3D", "ignoreList": [0]}