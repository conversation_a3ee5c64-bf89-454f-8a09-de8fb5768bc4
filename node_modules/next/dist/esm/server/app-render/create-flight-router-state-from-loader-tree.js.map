{"version": 3, "sources": ["../../../src/server/app-render/create-flight-router-state-from-loader-tree.ts"], "sourcesContent": ["import type { LoaderTree } from '../lib/app-dir-module'\nimport { HasLoadingBoundary, type FlightRouterState } from './types'\nimport type { GetDynamicParamFromSegment } from './app-render'\nimport { addSearchParamsIfPageSegment } from '../../shared/lib/segment'\n\nfunction createFlightRouterStateFromLoaderTreeImpl(\n  [segment, parallelRoutes, { layout, loading }]: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  searchParams: any,\n  includeHasLoadingBoundary: boolean,\n  didFindRootLayout: boolean\n): FlightRouterState {\n  const dynamicParam = getDynamicParamFromSegment(segment)\n  const treeSegment = dynamicParam ? dynamicParam.treeSegment : segment\n\n  const segmentTree: FlightRouterState = [\n    addSearchParamsIfPageSegment(treeSegment, searchParams),\n    {},\n  ]\n\n  // Mark the first segment that has a layout as the \"root\" layout\n  if (!didFindRootLayout && typeof layout !== 'undefined') {\n    didFindRootLayout = true\n    segmentTree[4] = true\n  }\n\n  let childHasLoadingBoundary = false\n  const children: FlightRouterState[1] = {}\n  Object.keys(parallelRoutes).forEach((parallelRouteKey) => {\n    const child = createFlightRouterStateFromLoaderTreeImpl(\n      parallelRoutes[parallelRouteKey],\n      getDynamicParamFromSegment,\n      searchParams,\n      includeHasLoadingBoundary,\n      didFindRootLayout\n    )\n    if (\n      includeHasLoadingBoundary &&\n      child[5] !== HasLoadingBoundary.SubtreeHasNoLoadingBoundary\n    ) {\n      childHasLoadingBoundary = true\n    }\n    children[parallelRouteKey] = child\n  })\n  segmentTree[1] = children\n\n  if (includeHasLoadingBoundary) {\n    // During a route tree prefetch, the FlightRouterState includes whether a\n    // tree has a loading boundary. The client uses this to determine if it can\n    // skip the data prefetch request — if `hasLoadingBoundary` is `false`, the\n    // data prefetch response will be empty, so there's no reason to request it.\n    // NOTE: It would be better to accumulate this while building the loader\n    // tree so we don't have to keep re-deriving it, but since this won't be\n    // once PPR is enabled everywhere, it's not that important.\n    segmentTree[5] = loading\n      ? HasLoadingBoundary.SegmentHasLoadingBoundary\n      : childHasLoadingBoundary\n        ? HasLoadingBoundary.SubtreeHasLoadingBoundary\n        : HasLoadingBoundary.SubtreeHasNoLoadingBoundary\n  }\n\n  return segmentTree\n}\n\nexport function createFlightRouterStateFromLoaderTree(\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  searchParams: any\n) {\n  const includeHasLoadingBoundary = false\n  const didFindRootLayout = false\n  return createFlightRouterStateFromLoaderTreeImpl(\n    loaderTree,\n    getDynamicParamFromSegment,\n    searchParams,\n    includeHasLoadingBoundary,\n    didFindRootLayout\n  )\n}\n\nexport function createRouteTreePrefetch(\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): FlightRouterState {\n  // Search params should not be added to page segment's cache key during a\n  // route tree prefetch request, because they do not affect the structure of\n  // the route. The client cache has its own logic to handle search params.\n  const searchParams = {}\n  // During a route tree prefetch, we include `hasLoadingBoundary` in\n  // the response.\n  const includeHasLoadingBoundary = true\n  const didFindRootLayout = false\n  return createFlightRouterStateFromLoaderTreeImpl(\n    loaderTree,\n    getDynamicParamFromSegment,\n    searchParams,\n    includeHasLoadingBoundary,\n    didFindRootLayout\n  )\n}\n"], "names": ["HasLoadingBoundary", "addSearchParamsIfPageSegment", "createFlightRouterStateFromLoaderTreeImpl", "segment", "parallelRoutes", "layout", "loading", "getDynamicParamFromSegment", "searchParams", "includeHasLoadingBoundary", "didFindRootLayout", "dynamicParam", "treeSegment", "segmentTree", "childHasLoadingBoundary", "children", "Object", "keys", "for<PERSON>ach", "parallelRouteKey", "child", "SubtreeHasNoLoadingBoundary", "SegmentHasLoadingBoundary", "SubtreeHasLoadingBoundary", "createFlightRouterStateFromLoaderTree", "loaderTree", "createRouteTreePrefetch"], "mappings": "AACA,SAASA,kBAAkB,QAAgC,UAAS;AAEpE,SAASC,4BAA4B,QAAQ,2BAA0B;AAEvE,SAASC,0CACP,CAACC,SAASC,gBAAgB,EAAEC,MAAM,EAAEC,OAAO,EAAE,CAAa,EAC1DC,0BAAsD,EACtDC,YAAiB,EACjBC,yBAAkC,EAClCC,iBAA0B;IAE1B,MAAMC,eAAeJ,2BAA2BJ;IAChD,MAAMS,cAAcD,eAAeA,aAAaC,WAAW,GAAGT;IAE9D,MAAMU,cAAiC;QACrCZ,6BAA6BW,aAAaJ;QAC1C,CAAC;KACF;IAED,gEAAgE;IAChE,IAAI,CAACE,qBAAqB,OAAOL,WAAW,aAAa;QACvDK,oBAAoB;QACpBG,WAAW,CAAC,EAAE,GAAG;IACnB;IAEA,IAAIC,0BAA0B;IAC9B,MAAMC,WAAiC,CAAC;IACxCC,OAAOC,IAAI,CAACb,gBAAgBc,OAAO,CAAC,CAACC;QACnC,MAAMC,QAAQlB,0CACZE,cAAc,CAACe,iBAAiB,EAChCZ,4BACAC,cACAC,2BACAC;QAEF,IACED,6BACAW,KAAK,CAAC,EAAE,KAAKpB,mBAAmBqB,2BAA2B,EAC3D;YACAP,0BAA0B;QAC5B;QACAC,QAAQ,CAACI,iBAAiB,GAAGC;IAC/B;IACAP,WAAW,CAAC,EAAE,GAAGE;IAEjB,IAAIN,2BAA2B;QAC7B,yEAAyE;QACzE,2EAA2E;QAC3E,2EAA2E;QAC3E,4EAA4E;QAC5E,wEAAwE;QACxE,wEAAwE;QACxE,2DAA2D;QAC3DI,WAAW,CAAC,EAAE,GAAGP,UACbN,mBAAmBsB,yBAAyB,GAC5CR,0BACEd,mBAAmBuB,yBAAyB,GAC5CvB,mBAAmBqB,2BAA2B;IACtD;IAEA,OAAOR;AACT;AAEA,OAAO,SAASW,sCACdC,UAAsB,EACtBlB,0BAAsD,EACtDC,YAAiB;IAEjB,MAAMC,4BAA4B;IAClC,MAAMC,oBAAoB;IAC1B,OAAOR,0CACLuB,YACAlB,4BACAC,cACAC,2BACAC;AAEJ;AAEA,OAAO,SAASgB,wBACdD,UAAsB,EACtBlB,0BAAsD;IAEtD,yEAAyE;IACzE,2EAA2E;IAC3E,yEAAyE;IACzE,MAAMC,eAAe,CAAC;IACtB,mEAAmE;IACnE,gBAAgB;IAChB,MAAMC,4BAA4B;IAClC,MAAMC,oBAAoB;IAC1B,OAAOR,0CACLuB,YACAlB,4BACAC,cACAC,2BACAC;AAEJ", "ignoreList": [0]}