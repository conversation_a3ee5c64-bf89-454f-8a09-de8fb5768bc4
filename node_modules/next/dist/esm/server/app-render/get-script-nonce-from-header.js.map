{"version": 3, "sources": ["../../../src/server/app-render/get-script-nonce-from-header.tsx"], "sourcesContent": ["import { ESCAPE_REGEX } from '../htmlescape'\n\nexport function getScriptNonceFromHeader(\n  cspHeaderValue: string\n): string | undefined {\n  const directives = cspHeaderValue\n    // Directives are split by ';'.\n    .split(';')\n    .map((directive) => directive.trim())\n\n  // First try to find the directive for the 'script-src', otherwise try to\n  // fallback to the 'default-src'.\n  const directive =\n    directives.find((dir) => dir.startsWith('script-src')) ||\n    directives.find((dir) => dir.startsWith('default-src'))\n\n  // If no directive could be found, then we're done.\n  if (!directive) {\n    return\n  }\n\n  // Extract the nonce from the directive\n  const nonce = directive\n    .split(' ')\n    // Remove the 'strict-src'/'default-src' string, this can't be the nonce.\n    .slice(1)\n    .map((source) => source.trim())\n    // Find the first source with the 'nonce-' prefix.\n    .find(\n      (source) =>\n        source.startsWith(\"'nonce-\") &&\n        source.length > 8 &&\n        source.endsWith(\"'\")\n    )\n    // Grab the nonce by trimming the 'nonce-' prefix.\n    ?.slice(7, -1)\n\n  // If we could't find the nonce, then we're done.\n  if (!nonce) {\n    return\n  }\n\n  // Don't accept the nonce value if it contains HTML escape characters.\n  // Technically, the spec requires a base64'd value, but this is just an\n  // extra layer.\n  if (ESCAPE_REGEX.test(nonce)) {\n    throw new Error(\n      'Nonce value from Content-Security-Policy contained HTML escape characters.\\nLearn more: https://nextjs.org/docs/messages/nonce-contained-invalid-characters'\n    )\n  }\n\n  return nonce\n}\n"], "names": ["ESCAPE_REGEX", "getScriptNonceFromHeader", "cspHeaderValue", "directive", "directives", "split", "map", "trim", "find", "dir", "startsWith", "nonce", "slice", "source", "length", "endsWith", "test", "Error"], "mappings": "AAAA,SAASA,YAAY,QAAQ,gBAAe;AAE5C,OAAO,SAASC,yBACdC,cAAsB;QAmBRC;IAjBd,MAAMC,aAAaF,cACjB,+BAA+B;KAC9BG,KAAK,CAAC,KACNC,GAAG,CAAC,CAACH,YAAcA,UAAUI,IAAI;IAEpC,yEAAyE;IACzE,iCAAiC;IACjC,MAAMJ,YACJC,WAAWI,IAAI,CAAC,CAACC,MAAQA,IAAIC,UAAU,CAAC,kBACxCN,WAAWI,IAAI,CAAC,CAACC,MAAQA,IAAIC,UAAU,CAAC;IAE1C,mDAAmD;IACnD,IAAI,CAACP,WAAW;QACd;IACF;IAEA,uCAAuC;IACvC,MAAMQ,SAAQR,kCAAAA,UACXE,KAAK,CAAC,IACP,yEAAyE;KACxEO,KAAK,CAAC,GACNN,GAAG,CAAC,CAACO,SAAWA,OAAON,IAAI,GAC5B,kDAAkD;KACjDC,IAAI,CACH,CAACK,SACCA,OAAOH,UAAU,CAAC,cAClBG,OAAOC,MAAM,GAAG,KAChBD,OAAOE,QAAQ,CAAC,0BAVRZ,gCAaVS,KAAK,CAAC,GAAG,CAAC;IAEd,iDAAiD;IACjD,IAAI,CAACD,OAAO;QACV;IACF;IAEA,sEAAsE;IACtE,uEAAuE;IACvE,eAAe;IACf,IAAIX,aAAagB,IAAI,CAACL,QAAQ;QAC5B,MAAM,qBAEL,CAFK,IAAIM,MACR,gKADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAON;AACT", "ignoreList": [0]}