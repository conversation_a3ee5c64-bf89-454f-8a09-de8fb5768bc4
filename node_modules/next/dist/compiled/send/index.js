(()=>{var e={186:(e,t,a)=>{
/*!
 * depd
 * Copyright(c) 2014-2018 <PERSON>
 * MIT Licensed
 */
var i=a(17).relative;e.exports=depd;var n=process.cwd();function containsNamespace(e,t){var a=e.split(/[ ,]+/);var i=String(t).toLowerCase();for(var n=0;n<a.length;n++){var r=a[n];if(r&&(r==="*"||r.toLowerCase()===i)){return true}}return false}function convertDataDescriptorToAccessor(e,t,a){var i=Object.getOwnPropertyDescriptor(e,t);var n=i.value;i.get=function getter(){return n};if(i.writable){i.set=function setter(e){return n=e}}delete i.value;delete i.writable;Object.defineProperty(e,t,i);return i}function createArgumentsString(e){var t="";for(var a=0;a<e;a++){t+=", arg"+a}return t.substr(2)}function createStackString(e){var t=this.name+": "+this.namespace;if(this.message){t+=" deprecated "+this.message}for(var a=0;a<e.length;a++){t+="\n    at "+e[a].toString()}return t}function depd(e){if(!e){throw new TypeError("argument namespace is required")}var t=getStack();var a=callSiteLocation(t[1]);var i=a[0];function deprecate(e){log.call(deprecate,e)}deprecate._file=i;deprecate._ignored=isignored(e);deprecate._namespace=e;deprecate._traced=istraced(e);deprecate._warned=Object.create(null);deprecate.function=wrapfunction;deprecate.property=wrapproperty;return deprecate}function eehaslisteners(e,t){var a=typeof e.listenerCount!=="function"?e.listeners(t).length:e.listenerCount(t);return a>0}function isignored(e){if(process.noDeprecation){return true}var t=process.env.NO_DEPRECATION||"";return containsNamespace(t,e)}function istraced(e){if(process.traceDeprecation){return true}var t=process.env.TRACE_DEPRECATION||"";return containsNamespace(t,e)}function log(e,t){var a=eehaslisteners(process,"deprecation");if(!a&&this._ignored){return}var i;var n;var r;var o;var p=0;var s=false;var c=getStack();var l=this._file;if(t){o=t;r=callSiteLocation(c[1]);r.name=o.name;l=r[0]}else{p=2;o=callSiteLocation(c[p]);r=o}for(;p<c.length;p++){i=callSiteLocation(c[p]);n=i[0];if(n===l){s=true}else if(n===this._file){l=this._file}else if(s){break}}var d=i?o.join(":")+"__"+i.join(":"):undefined;if(d!==undefined&&d in this._warned){return}this._warned[d]=true;var m=e;if(!m){m=r===o||!r.name?defaultMessage(o):defaultMessage(r)}if(a){var u=DeprecationError(this._namespace,m,c.slice(p));process.emit("deprecation",u);return}var v=process.stderr.isTTY?formatColor:formatPlain;var f=v.call(this,m,i,c.slice(p));process.stderr.write(f+"\n","utf8")}function callSiteLocation(e){var t=e.getFileName()||"<anonymous>";var a=e.getLineNumber();var i=e.getColumnNumber();if(e.isEval()){t=e.getEvalOrigin()+", "+t}var n=[t,a,i];n.callSite=e;n.name=e.getFunctionName();return n}function defaultMessage(e){var t=e.callSite;var a=e.name;if(!a){a="<anonymous@"+formatLocation(e)+">"}var i=t.getThis();var n=i&&t.getTypeName();if(n==="Object"){n=undefined}if(n==="Function"){n=i.name||n}return n&&t.getMethodName()?n+"."+a:a}function formatPlain(e,t,a){var i=(new Date).toUTCString();var n=i+" "+this._namespace+" deprecated "+e;if(this._traced){for(var r=0;r<a.length;r++){n+="\n    at "+a[r].toString()}return n}if(t){n+=" at "+formatLocation(t)}return n}function formatColor(e,t,a){var i="[36;1m"+this._namespace+"[22;39m"+" [33;1mdeprecated[22;39m"+" [0m"+e+"[39m";if(this._traced){for(var n=0;n<a.length;n++){i+="\n    [36mat "+a[n].toString()+"[39m"}return i}if(t){i+=" [36m"+formatLocation(t)+"[39m"}return i}function formatLocation(e){return i(n,e[0])+":"+e[1]+":"+e[2]}function getStack(){var e=Error.stackTraceLimit;var t={};var a=Error.prepareStackTrace;Error.prepareStackTrace=prepareObjectStackTrace;Error.stackTraceLimit=Math.max(10,e);Error.captureStackTrace(t);var i=t.stack.slice(1);Error.prepareStackTrace=a;Error.stackTraceLimit=e;return i}function prepareObjectStackTrace(e,t){return t}function wrapfunction(e,t){if(typeof e!=="function"){throw new TypeError("argument fn must be a function")}var a=createArgumentsString(e.length);var i=getStack();var n=callSiteLocation(i[1]);n.name=e.name;var r=new Function("fn","log","deprecate","message","site",'"use strict"\n'+"return function ("+a+") {"+"log.call(deprecate, message, site)\n"+"return fn.apply(this, arguments)\n"+"}")(e,log,this,t,n);return r}function wrapproperty(e,t,a){if(!e||typeof e!=="object"&&typeof e!=="function"){throw new TypeError("argument obj must be object")}var i=Object.getOwnPropertyDescriptor(e,t);if(!i){throw new TypeError("must call property on owner object")}if(!i.configurable){throw new TypeError("property must be configurable")}var n=this;var r=getStack();var o=callSiteLocation(r[1]);o.name=t;if("value"in i){i=convertDataDescriptorToAccessor(e,t,a)}var p=i.get;var s=i.set;if(typeof p==="function"){i.get=function getter(){log.call(n,a,o);return p.apply(this,arguments)}}if(typeof s==="function"){i.set=function setter(){log.call(n,a,o);return s.apply(this,arguments)}}Object.defineProperty(e,t,i)}function DeprecationError(e,t,a){var i=new Error;var n;Object.defineProperty(i,"constructor",{value:DeprecationError});Object.defineProperty(i,"message",{configurable:true,enumerable:false,value:t,writable:true});Object.defineProperty(i,"name",{enumerable:false,configurable:true,value:"DeprecationError",writable:true});Object.defineProperty(i,"namespace",{configurable:true,enumerable:false,value:e,writable:true});Object.defineProperty(i,"stack",{configurable:true,enumerable:false,get:function(){if(n!==undefined){return n}return n=createStackString.call(this,a)},set:function setter(e){n=e}});return i}},397:(e,t,a)=>{"use strict";
/*!
 * destroy
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2015-2022 Douglas Christopher Wilson
 * MIT Licensed
 */var i=a(361).EventEmitter;var n=a(147).ReadStream;var r=a(781);var o=a(796);e.exports=destroy;function destroy(e,t){if(isFsReadStream(e)){destroyReadStream(e)}else if(isZlibStream(e)){destroyZlibStream(e)}else if(hasDestroy(e)){e.destroy()}if(isEventEmitter(e)&&t){e.removeAllListeners("error");e.addListener("error",noop)}return e}function destroyReadStream(e){e.destroy();if(typeof e.close==="function"){e.on("open",onOpenClose)}}function closeZlibStream(e){if(e._hadError===true){var t=e._binding===null?"_binding":"_handle";e[t]={close:function(){this[t]=null}}}e.close()}function destroyZlibStream(e){if(typeof e.destroy==="function"){if(e._binding){e.destroy();if(e._processing){e._needDrain=true;e.once("drain",onDrainClearBinding)}else{e._binding.clear()}}else if(e._destroy&&e._destroy!==r.Transform.prototype._destroy){e.destroy()}else if(e._destroy&&typeof e.close==="function"){e.destroyed=true;e.close()}else{e.destroy()}}else if(typeof e.close==="function"){closeZlibStream(e)}}function hasDestroy(e){return e instanceof r&&typeof e.destroy==="function"}function isEventEmitter(e){return e instanceof i}function isFsReadStream(e){return e instanceof n}function isZlibStream(e){return e instanceof o.Gzip||e instanceof o.Gunzip||e instanceof o.Deflate||e instanceof o.DeflateRaw||e instanceof o.Inflate||e instanceof o.InflateRaw||e instanceof o.Unzip}function noop(){}function onDrainClearBinding(){this._binding.clear()}function onOpenClose(){if(typeof this.fd==="number"){this.close()}}},481:e=>{"use strict";
/*!
 * ee-first
 * Copyright(c) 2014 Jonathan Ong
 * MIT Licensed
 */e.exports=first;function first(e,t){if(!Array.isArray(e))throw new TypeError("arg must be an array of [ee, events...] arrays");var a=[];for(var i=0;i<e.length;i++){var n=e[i];if(!Array.isArray(n)||n.length<2)throw new TypeError("each array member must be [ee, events...]");var r=n[0];for(var o=1;o<n.length;o++){var p=n[o];var s=listener(p,callback);r.on(p,s);a.push({ee:r,event:p,fn:s})}}function callback(){cleanup();t.apply(null,arguments)}function cleanup(){var e;for(var t=0;t<a.length;t++){e=a[t];e.ee.removeListener(e.event,e.fn)}}function thunk(e){t=e}thunk.cancel=cleanup;return thunk}function listener(e,t){return function onevent(a){var i=new Array(arguments.length);var n=this;var r=e==="error"?a:null;for(var o=0;o<i.length;o++){i[o]=arguments[o]}t(r,n,e,i)}}},927:e=>{"use strict";
/*!
 * encodeurl
 * Copyright(c) 2016 Douglas Christopher Wilson
 * MIT Licensed
 */e.exports=encodeUrl;var t=/(?:[^\x21\x25\x26-\x3B\x3D\x3F-\x5B\x5D\x5F\x61-\x7A\x7E]|%(?:[^0-9A-Fa-f]|[0-9A-Fa-f][^0-9A-Fa-f]|$))+/g;var a=/(^|[^\uD800-\uDBFF])[\uDC00-\uDFFF]|[\uD800-\uDBFF]([^\uDC00-\uDFFF]|$)/g;var i="$1�$2";function encodeUrl(e){return String(e).replace(a,i).replace(t,encodeURI)}},354:e=>{"use strict";
/*!
 * escape-html
 * Copyright(c) 2012-2013 TJ Holowaychuk
 * Copyright(c) 2015 Andreas Lubbe
 * Copyright(c) 2015 Tiancheng "Timothy" Gu
 * MIT Licensed
 */var t=/["'&<>]/;e.exports=escapeHtml;function escapeHtml(e){var a=""+e;var i=t.exec(a);if(!i){return a}var n;var r="";var o=0;var p=0;for(o=i.index;o<a.length;o++){switch(a.charCodeAt(o)){case 34:n="&quot;";break;case 38:n="&amp;";break;case 39:n="&#39;";break;case 60:n="&lt;";break;case 62:n="&gt;";break;default:continue}if(p!==o){r+=a.substring(p,o)}p=o+1;r+=n}return p!==o?r+a.substring(p,o):r}},516:(e,t,a)=>{"use strict";
/*!
 * etag
 * Copyright(c) 2014-2016 Douglas Christopher Wilson
 * MIT Licensed
 */e.exports=etag;var i=a(113);var n=a(147).Stats;var r=Object.prototype.toString;function entitytag(e){if(e.length===0){return'"0-2jmj7l5rSw0yVb/vlWAYkK/YBwk"'}var t=i.createHash("sha1").update(e,"utf8").digest("base64").substring(0,27);var a=typeof e==="string"?Buffer.byteLength(e,"utf8"):e.length;return'"'+a.toString(16)+"-"+t+'"'}function etag(e,t){if(e==null){throw new TypeError("argument entity is required")}var a=isstats(e);var i=t&&typeof t.weak==="boolean"?t.weak:a;if(!a&&typeof e!=="string"&&!Buffer.isBuffer(e)){throw new TypeError("argument entity must be string, Buffer, or fs.Stats")}var n=a?stattag(e):entitytag(e);return i?"W/"+n:n}function isstats(e){if(typeof n==="function"&&e instanceof n){return true}return e&&typeof e==="object"&&"ctime"in e&&r.call(e.ctime)==="[object Date]"&&"mtime"in e&&r.call(e.mtime)==="[object Date]"&&"ino"in e&&typeof e.ino==="number"&&"size"in e&&typeof e.size==="number"}function stattag(e){var t=e.mtime.getTime().toString(16);var a=e.size.toString(16);return'"'+a+"-"+t+'"'}},546:(e,t,a)=>{"use strict";
/*!
 * http-errors
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2016 Douglas Christopher Wilson
 * MIT Licensed
 */var i=a(186)("http-errors");var n=a(832);var r=a(596);var o=a(842);var p=a(637);e.exports=createError;e.exports.HttpError=createHttpErrorConstructor();e.exports.isHttpError=createIsHttpErrorFunction(e.exports.HttpError);populateConstructorExports(e.exports,r.codes,e.exports.HttpError);function codeClass(e){return Number(String(e).charAt(0)+"00")}function createError(){var e;var t;var a=500;var n={};for(var o=0;o<arguments.length;o++){var p=arguments[o];var s=typeof p;if(s==="object"&&p instanceof Error){e=p;a=e.status||e.statusCode||a}else if(s==="number"&&o===0){a=p}else if(s==="string"){t=p}else if(s==="object"){n=p}else{throw new TypeError("argument #"+(o+1)+" unsupported type "+s)}}if(typeof a==="number"&&(a<400||a>=600)){i("non-error status code; use only 4xx or 5xx status codes")}if(typeof a!=="number"||!r.message[a]&&(a<400||a>=600)){a=500}var c=createError[a]||createError[codeClass(a)];if(!e){e=c?new c(t):new Error(t||r.message[a]);Error.captureStackTrace(e,createError)}if(!c||!(e instanceof c)||e.status!==a){e.expose=a<500;e.status=e.statusCode=a}for(var l in n){if(l!=="status"&&l!=="statusCode"){e[l]=n[l]}}return e}function createHttpErrorConstructor(){function HttpError(){throw new TypeError("cannot construct abstract class")}o(HttpError,Error);return HttpError}function createClientErrorConstructor(e,t,a){var i=toClassName(t);function ClientError(e){var t=e!=null?e:r.message[a];var o=new Error(t);Error.captureStackTrace(o,ClientError);n(o,ClientError.prototype);Object.defineProperty(o,"message",{enumerable:true,configurable:true,value:t,writable:true});Object.defineProperty(o,"name",{enumerable:false,configurable:true,value:i,writable:true});return o}o(ClientError,e);nameFunc(ClientError,i);ClientError.prototype.status=a;ClientError.prototype.statusCode=a;ClientError.prototype.expose=true;return ClientError}function createIsHttpErrorFunction(e){return function isHttpError(t){if(!t||typeof t!=="object"){return false}if(t instanceof e){return true}return t instanceof Error&&typeof t.expose==="boolean"&&typeof t.statusCode==="number"&&t.status===t.statusCode}}function createServerErrorConstructor(e,t,a){var i=toClassName(t);function ServerError(e){var t=e!=null?e:r.message[a];var o=new Error(t);Error.captureStackTrace(o,ServerError);n(o,ServerError.prototype);Object.defineProperty(o,"message",{enumerable:true,configurable:true,value:t,writable:true});Object.defineProperty(o,"name",{enumerable:false,configurable:true,value:i,writable:true});return o}o(ServerError,e);nameFunc(ServerError,i);ServerError.prototype.status=a;ServerError.prototype.statusCode=a;ServerError.prototype.expose=false;return ServerError}function nameFunc(e,t){var a=Object.getOwnPropertyDescriptor(e,"name");if(a&&a.configurable){a.value=t;Object.defineProperty(e,"name",a)}}function populateConstructorExports(e,t,a){t.forEach((function forEachCode(t){var i;var n=p(r.message[t]);switch(codeClass(t)){case 400:i=createClientErrorConstructor(a,n,t);break;case 500:i=createServerErrorConstructor(a,n,t);break}if(i){e[t]=i;e[n]=i}}))}function toClassName(e){return e.substr(-5)!=="Error"?e+"Error":e}},842:(e,t,a)=>{try{var i=a(837);if(typeof i.inherits!=="function")throw"";e.exports=i.inherits}catch(t){e.exports=a(782)}},782:e=>{if(typeof Object.create==="function"){e.exports=function inherits(e,t){if(t){e.super_=t;e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}})}}}else{e.exports=function inherits(e,t){if(t){e.super_=t;var TempCtor=function(){};TempCtor.prototype=t.prototype;e.prototype=new TempCtor;e.prototype.constructor=e}}}},396:(e,t,a)=>{var i=a(17);var n=a(147);function Mime(){this.types=Object.create(null);this.extensions=Object.create(null)}Mime.prototype.define=function(e){for(var t in e){var a=e[t];for(var i=0;i<a.length;i++){if(process.env.DEBUG_MIME&&this.types[a[i]]){console.warn((this._loading||"define()").replace(/.*\//,""),'changes "'+a[i]+'" extension type from '+this.types[a[i]]+" to "+t)}this.types[a[i]]=t}if(!this.extensions[t]){this.extensions[t]=a[0]}}};Mime.prototype.load=function(e){this._loading=e;var t={},a=n.readFileSync(e,"ascii"),i=a.split(/[\r\n]+/);i.forEach((function(e){var a=e.replace(/\s*#.*|^\s*|\s*$/g,"").split(/\s+/);t[a.shift()]=a}));this.define(t);this._loading=null};Mime.prototype.lookup=function(e,t){var a=e.replace(/^.*[\.\/\\]/,"").toLowerCase();return this.types[a]||t||this.default_type};Mime.prototype.extension=function(e){var t=e.match(/^\s*([^;\s]*)(?:;|\s|$)/)[1].toLowerCase();return this.extensions[t]};var r=new Mime;r.define(a(598));r.default_type=r.lookup("bin");r.Mime=Mime;r.charsets={lookup:function(e,t){return/^text\/|^application\/(javascript|json)/.test(e)?"UTF-8":t}};e.exports=r},958:e=>{var t=1e3;var a=t*60;var i=a*60;var n=i*24;var r=n*7;var o=n*365.25;e.exports=function(e,t){t=t||{};var a=typeof e;if(a==="string"&&e.length>0){return parse(e)}else if(a==="number"&&isFinite(e)){return t.long?fmtLong(e):fmtShort(e)}throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function parse(e){e=String(e);if(e.length>100){return}var p=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!p){return}var s=parseFloat(p[1]);var c=(p[2]||"ms").toLowerCase();switch(c){case"years":case"year":case"yrs":case"yr":case"y":return s*o;case"weeks":case"week":case"w":return s*r;case"days":case"day":case"d":return s*n;case"hours":case"hour":case"hrs":case"hr":case"h":return s*i;case"minutes":case"minute":case"mins":case"min":case"m":return s*a;case"seconds":case"second":case"secs":case"sec":case"s":return s*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return s;default:return undefined}}function fmtShort(e){var r=Math.abs(e);if(r>=n){return Math.round(e/n)+"d"}if(r>=i){return Math.round(e/i)+"h"}if(r>=a){return Math.round(e/a)+"m"}if(r>=t){return Math.round(e/t)+"s"}return e+"ms"}function fmtLong(e){var r=Math.abs(e);if(r>=n){return plural(e,r,n,"day")}if(r>=i){return plural(e,r,i,"hour")}if(r>=a){return plural(e,r,a,"minute")}if(r>=t){return plural(e,r,t,"second")}return e+" ms"}function plural(e,t,a,i){var n=t>=a*1.5;return Math.round(e/a)+" "+i+(n?"s":"")}},71:(e,t,a)=>{"use strict";
/*!
 * on-finished
 * Copyright(c) 2013 Jonathan Ong
 * Copyright(c) 2014 Douglas Christopher Wilson
 * MIT Licensed
 */e.exports=onFinished;e.exports.isFinished=isFinished;var i=tryRequireAsyncHooks();var n=a(481);var r=typeof setImmediate==="function"?setImmediate:function(e){process.nextTick(e.bind.apply(e,arguments))};function onFinished(e,t){if(isFinished(e)!==false){r(t,null,e);return e}attachListener(e,wrap(t));return e}function isFinished(e){var t=e.socket;if(typeof e.finished==="boolean"){return Boolean(e.finished||t&&!t.writable)}if(typeof e.complete==="boolean"){return Boolean(e.upgrade||!t||!t.readable||e.complete&&!e.readable)}return undefined}function attachFinishedListener(e,t){var a;var i;var r=false;function onFinish(e){a.cancel();i.cancel();r=true;t(e)}a=i=n([[e,"end","finish"]],onFinish);function onSocket(t){e.removeListener("socket",onSocket);if(r)return;if(a!==i)return;i=n([[t,"error","close"]],onFinish)}if(e.socket){onSocket(e.socket);return}e.on("socket",onSocket);if(e.socket===undefined){patchAssignSocket(e,onSocket)}}function attachListener(e,t){var a=e.__onFinished;if(!a||!a.queue){a=e.__onFinished=createListener(e);attachFinishedListener(e,a)}a.queue.push(t)}function createListener(e){function listener(t){if(e.__onFinished===listener)e.__onFinished=null;if(!listener.queue)return;var a=listener.queue;listener.queue=null;for(var i=0;i<a.length;i++){a[i](t,e)}}listener.queue=[];return listener}function patchAssignSocket(e,t){var a=e.assignSocket;if(typeof a!=="function")return;e.assignSocket=function _assignSocket(e){a.call(this,e);t(e)}}function tryRequireAsyncHooks(){try{return a(852)}catch(e){return{}}}function wrap(e){var t;if(i.AsyncResource){t=new i.AsyncResource(e.name||"bound-anonymous-fn")}if(!t||!t.runInAsyncScope){return e}return t.runInAsyncScope.bind(t,e,null)}},953:e=>{"use strict";
/*!
 * range-parser
 * Copyright(c) 2012-2014 TJ Holowaychuk
 * Copyright(c) 2015-2016 Douglas Christopher Wilson
 * MIT Licensed
 */e.exports=rangeParser;function rangeParser(e,t,a){if(typeof t!=="string"){throw new TypeError("argument str must be a string")}var i=t.indexOf("=");if(i===-1){return-2}var n=t.slice(i+1).split(",");var r=[];r.type=t.slice(0,i);for(var o=0;o<n.length;o++){var p=n[o].split("-");var s=parseInt(p[0],10);var c=parseInt(p[1],10);if(isNaN(s)){s=e-c;c=e-1}else if(isNaN(c)){c=e-1}if(c>e-1){c=e-1}if(isNaN(s)||isNaN(c)||s>c||s<0){continue}r.push({start:s,end:c})}if(r.length<1){return-1}return a&&a.combine?combineRanges(r):r}function combineRanges(e){var t=e.map(mapWithIndex).sort(sortByRangeStart);for(var a=0,i=1;i<t.length;i++){var n=t[i];var r=t[a];if(n.start>r.end+1){t[++a]=n}else if(n.end>r.end){r.end=n.end;r.index=Math.min(r.index,n.index)}}t.length=a+1;var o=t.sort(sortByRangeIndex).map(mapWithoutIndex);o.type=e.type;return o}function mapWithIndex(e,t){return{start:e.start,end:e.end,index:t}}function mapWithoutIndex(e){return{start:e.start,end:e.end}}function sortByRangeIndex(e,t){return e.index-t.index}function sortByRangeStart(e,t){return e.start-t.start}},228:(e,t,a)=>{"use strict";
/*!
 * send
 * Copyright(c) 2012 TJ Holowaychuk
 * Copyright(c) 2014-2022 Douglas Christopher Wilson
 * MIT Licensed
 */var i=a(546);var n=a(937)("send");var r=a(186)("send");var o=a(397);var p=a(927);var s=a(354);var c=a(516);var l=a(890);var d=a(147);var m=a(396);var u=a(958);var v=a(71);var f=a(953);var x=a(17);var g=a(596);var h=a(781);var b=a(837);var y=x.extname;var w=x.join;var k=x.normalize;var S=x.resolve;var j=x.sep;var _=/^ *bytes=/;var E=60*60*24*365*1e3;var C=/(?:^|[\\/])\.\.(?:[\\/]|$)/;e.exports=send;e.exports.mime=m;function send(e,t,a){return new SendStream(e,t,a)}function SendStream(e,t,a){h.call(this);var i=a||{};this.options=i;this.path=t;this.req=e;this._acceptRanges=i.acceptRanges!==undefined?Boolean(i.acceptRanges):true;this._cacheControl=i.cacheControl!==undefined?Boolean(i.cacheControl):true;this._etag=i.etag!==undefined?Boolean(i.etag):true;this._dotfiles=i.dotfiles!==undefined?i.dotfiles:"ignore";if(this._dotfiles!=="ignore"&&this._dotfiles!=="allow"&&this._dotfiles!=="deny"){throw new TypeError('dotfiles option must be "allow", "deny", or "ignore"')}this._hidden=Boolean(i.hidden);if(i.hidden!==undefined){r("hidden: use dotfiles: '"+(this._hidden?"allow":"ignore")+"' instead")}if(i.dotfiles===undefined){this._dotfiles=undefined}this._extensions=i.extensions!==undefined?normalizeList(i.extensions,"extensions option"):[];this._immutable=i.immutable!==undefined?Boolean(i.immutable):false;this._index=i.index!==undefined?normalizeList(i.index,"index option"):["index.html"];this._lastModified=i.lastModified!==undefined?Boolean(i.lastModified):true;this._maxage=i.maxAge||i.maxage;this._maxage=typeof this._maxage==="string"?u(this._maxage):Number(this._maxage);this._maxage=!isNaN(this._maxage)?Math.min(Math.max(0,this._maxage),E):0;this._root=i.root?S(i.root):null;if(!this._root&&i.from){this.from(i.from)}}b.inherits(SendStream,h);SendStream.prototype.etag=r.function((function etag(e){this._etag=Boolean(e);n("etag %s",this._etag);return this}),"send.etag: pass etag as option");SendStream.prototype.hidden=r.function((function hidden(e){this._hidden=Boolean(e);this._dotfiles=undefined;n("hidden %s",this._hidden);return this}),"send.hidden: use dotfiles option");SendStream.prototype.index=r.function((function index(e){var index=!e?[]:normalizeList(e,"paths argument");n("index %o",e);this._index=index;return this}),"send.index: pass index as option");SendStream.prototype.root=function root(e){this._root=S(String(e));n("root %s",this._root);return this};SendStream.prototype.from=r.function(SendStream.prototype.root,"send.from: pass root as option");SendStream.prototype.root=r.function(SendStream.prototype.root,"send.root: pass root as option");SendStream.prototype.maxage=r.function((function maxage(e){this._maxage=typeof e==="string"?u(e):Number(e);this._maxage=!isNaN(this._maxage)?Math.min(Math.max(0,this._maxage),E):0;n("max-age %d",this._maxage);return this}),"send.maxage: pass maxAge as option");SendStream.prototype.error=function error(e,t){if(hasListeners(this,"error")){return this.emit("error",createHttpError(e,t))}var a=this.res;var i=g.message[e]||String(e);var n=createHtmlDocument("Error",s(i));clearHeaders(a);if(t&&t.headers){setHeaders(a,t.headers)}a.statusCode=e;a.setHeader("Content-Type","text/html; charset=UTF-8");a.setHeader("Content-Length",Buffer.byteLength(n));a.setHeader("Content-Security-Policy","default-src 'none'");a.setHeader("X-Content-Type-Options","nosniff");a.end(n)};SendStream.prototype.hasTrailingSlash=function hasTrailingSlash(){return this.path[this.path.length-1]==="/"};SendStream.prototype.isConditionalGET=function isConditionalGET(){return this.req.headers["if-match"]||this.req.headers["if-unmodified-since"]||this.req.headers["if-none-match"]||this.req.headers["if-modified-since"]};SendStream.prototype.isPreconditionFailure=function isPreconditionFailure(){var e=this.req;var t=this.res;var a=e.headers["if-match"];if(a){var i=t.getHeader("ETag");return!i||a!=="*"&&parseTokenList(a).every((function(e){return e!==i&&e!=="W/"+i&&"W/"+e!==i}))}var n=parseHttpDate(e.headers["if-unmodified-since"]);if(!isNaN(n)){var r=parseHttpDate(t.getHeader("Last-Modified"));return isNaN(r)||r>n}return false};SendStream.prototype.removeContentHeaderFields=function removeContentHeaderFields(){var e=this.res;e.removeHeader("Content-Encoding");e.removeHeader("Content-Language");e.removeHeader("Content-Length");e.removeHeader("Content-Range");e.removeHeader("Content-Type")};SendStream.prototype.notModified=function notModified(){var e=this.res;n("not modified");this.removeContentHeaderFields();e.statusCode=304;e.end()};SendStream.prototype.headersAlreadySent=function headersAlreadySent(){var e=new Error("Can't set headers after they are sent.");n("headers already sent");this.error(500,e)};SendStream.prototype.isCachable=function isCachable(){var e=this.res.statusCode;return e>=200&&e<300||e===304};SendStream.prototype.onStatError=function onStatError(e){switch(e.code){case"ENAMETOOLONG":case"ENOENT":case"ENOTDIR":this.error(404,e);break;default:this.error(500,e);break}};SendStream.prototype.isFresh=function isFresh(){return l(this.req.headers,{etag:this.res.getHeader("ETag"),"last-modified":this.res.getHeader("Last-Modified")})};SendStream.prototype.isRangeFresh=function isRangeFresh(){var e=this.req.headers["if-range"];if(!e){return true}if(e.indexOf('"')!==-1){var t=this.res.getHeader("ETag");return Boolean(t&&e.indexOf(t)!==-1)}var a=this.res.getHeader("Last-Modified");return parseHttpDate(a)<=parseHttpDate(e)};SendStream.prototype.redirect=function redirect(e){var t=this.res;if(hasListeners(this,"directory")){this.emit("directory",t,e);return}if(this.hasTrailingSlash()){this.error(403);return}var a=p(collapseLeadingSlashes(this.path+"/"));var i=createHtmlDocument("Redirecting",'Redirecting to <a href="'+s(a)+'">'+s(a)+"</a>");t.statusCode=301;t.setHeader("Content-Type","text/html; charset=UTF-8");t.setHeader("Content-Length",Buffer.byteLength(i));t.setHeader("Content-Security-Policy","default-src 'none'");t.setHeader("X-Content-Type-Options","nosniff");t.setHeader("Location",a);t.end(i)};SendStream.prototype.pipe=function pipe(e){var t=this._root;this.res=e;var a=decode(this.path);if(a===-1){this.error(400);return e}if(~a.indexOf("\0")){this.error(400);return e}var i;if(t!==null){if(a){a=k("."+j+a)}if(C.test(a)){n('malicious path "%s"',a);this.error(403);return e}i=a.split(j);a=k(w(t,a))}else{if(C.test(a)){n('malicious path "%s"',a);this.error(403);return e}i=k(a).split(j);a=S(a)}if(containsDotFile(i)){var r=this._dotfiles;if(r===undefined){r=i[i.length-1][0]==="."?this._hidden?"allow":"ignore":"allow"}n('%s dotfile "%s"',r,a);switch(r){case"allow":break;case"deny":this.error(403);return e;case"ignore":default:this.error(404);return e}}if(this._index.length&&this.hasTrailingSlash()){this.sendIndex(a);return e}this.sendFile(a);return e};SendStream.prototype.send=function send(e,t){var a=t.size;var i=this.options;var r={};var o=this.res;var p=this.req;var s=p.headers.range;var c=i.start||0;if(headersSent(o)){this.headersAlreadySent();return}n('pipe "%s"',e);this.setHeader(e,t);this.type(e);if(this.isConditionalGET()){if(this.isPreconditionFailure()){this.error(412);return}if(this.isCachable()&&this.isFresh()){this.notModified();return}}a=Math.max(0,a-c);if(i.end!==undefined){var l=i.end-c+1;if(a>l)a=l}if(this._acceptRanges&&_.test(s)){s=f(a,s,{combine:true});if(!this.isRangeFresh()){n("range stale");s=-2}if(s===-1){n("range unsatisfiable");o.setHeader("Content-Range",contentRange("bytes",a));return this.error(416,{headers:{"Content-Range":o.getHeader("Content-Range")}})}if(s!==-2&&s.length===1){n("range %j",s);o.statusCode=206;o.setHeader("Content-Range",contentRange("bytes",a,s[0]));c+=s[0].start;a=s[0].end-s[0].start+1}}for(var d in i){r[d]=i[d]}r.start=c;r.end=Math.max(c,c+a-1);o.setHeader("Content-Length",a);if(p.method==="HEAD"){o.end();return}this.stream(e,r)};SendStream.prototype.sendFile=function sendFile(e){var t=0;var a=this;n('stat "%s"',e);d.stat(e,(function onstat(t,i){if(t&&t.code==="ENOENT"&&!y(e)&&e[e.length-1]!==j){return next(t)}if(t)return a.onStatError(t);if(i.isDirectory())return a.redirect(e);a.emit("file",e,i);a.send(e,i)}));function next(i){if(a._extensions.length<=t){return i?a.onStatError(i):a.error(404)}var r=e+"."+a._extensions[t++];n('stat "%s"',r);d.stat(r,(function(e,t){if(e)return next(e);if(t.isDirectory())return next();a.emit("file",r,t);a.send(r,t)}))}};SendStream.prototype.sendIndex=function sendIndex(e){var t=-1;var a=this;function next(i){if(++t>=a._index.length){if(i)return a.onStatError(i);return a.error(404)}var r=w(e,a._index[t]);n('stat "%s"',r);d.stat(r,(function(e,t){if(e)return next(e);if(t.isDirectory())return next();a.emit("file",r,t);a.send(r,t)}))}next()};SendStream.prototype.stream=function stream(e,t){var a=this;var i=this.res;var stream=d.createReadStream(e,t);this.emit("stream",stream);stream.pipe(i);function cleanup(){o(stream,true)}v(i,cleanup);stream.on("error",(function onerror(e){cleanup();a.onStatError(e)}));stream.on("end",(function onend(){a.emit("end")}))};SendStream.prototype.type=function type(e){var t=this.res;if(t.getHeader("Content-Type"))return;var type=m.lookup(e);if(!type){n("no content-type");return}var a=m.charsets.lookup(type);n("content-type %s",type);t.setHeader("Content-Type",type+(a?"; charset="+a:""))};SendStream.prototype.setHeader=function setHeader(e,t){var a=this.res;this.emit("headers",a,e,t);if(this._acceptRanges&&!a.getHeader("Accept-Ranges")){n("accept ranges");a.setHeader("Accept-Ranges","bytes")}if(this._cacheControl&&!a.getHeader("Cache-Control")){var i="public, max-age="+Math.floor(this._maxage/1e3);if(this._immutable){i+=", immutable"}n("cache-control %s",i);a.setHeader("Cache-Control",i)}if(this._lastModified&&!a.getHeader("Last-Modified")){var r=t.mtime.toUTCString();n("modified %s",r);a.setHeader("Last-Modified",r)}if(this._etag&&!a.getHeader("ETag")){var o=c(t);n("etag %s",o);a.setHeader("ETag",o)}};function clearHeaders(e){var t=getHeaderNames(e);for(var a=0;a<t.length;a++){e.removeHeader(t[a])}}function collapseLeadingSlashes(e){for(var t=0;t<e.length;t++){if(e[t]!=="/"){break}}return t>1?"/"+e.substr(t):e}function containsDotFile(e){for(var t=0;t<e.length;t++){var a=e[t];if(a.length>1&&a[0]==="."){return true}}return false}function contentRange(e,t,a){return e+" "+(a?a.start+"-"+a.end:"*")+"/"+t}function createHtmlDocument(e,t){return"<!DOCTYPE html>\n"+'<html lang="en">\n'+"<head>\n"+'<meta charset="utf-8">\n'+"<title>"+e+"</title>\n"+"</head>\n"+"<body>\n"+"<pre>"+t+"</pre>\n"+"</body>\n"+"</html>\n"}function createHttpError(e,t){if(!t){return i(e)}return t instanceof Error?i(e,t,{expose:false}):i(e,t)}function decode(e){try{return decodeURIComponent(e)}catch(e){return-1}}function getHeaderNames(e){return typeof e.getHeaderNames!=="function"?Object.keys(e._headers||{}):e.getHeaderNames()}function hasListeners(e,t){var a=typeof e.listenerCount!=="function"?e.listeners(t).length:e.listenerCount(t);return a>0}function headersSent(e){return typeof e.headersSent!=="boolean"?Boolean(e._header):e.headersSent}function normalizeList(e,t){var a=[].concat(e||[]);for(var i=0;i<a.length;i++){if(typeof a[i]!=="string"){throw new TypeError(t+" must be array of strings or false")}}return a}function parseHttpDate(e){var t=e&&Date.parse(e);return typeof t==="number"?t:NaN}function parseTokenList(e){var t=0;var a=[];var i=0;for(var n=0,r=e.length;n<r;n++){switch(e.charCodeAt(n)){case 32:if(i===t){i=t=n+1}break;case 44:if(i!==t){a.push(e.substring(i,t))}i=t=n+1;break;default:t=n+1;break}}if(i!==t){a.push(e.substring(i,t))}return a}function setHeaders(e,t){var a=Object.keys(t);for(var i=0;i<a.length;i++){var n=a[i];e.setHeader(n,t[n])}}},832:e=>{"use strict";e.exports=Object.setPrototypeOf||({__proto__:[]}instanceof Array?setProtoOf:mixinProperties);function setProtoOf(e,t){e.__proto__=t;return e}function mixinProperties(e,t){for(var a in t){if(!Object.prototype.hasOwnProperty.call(e,a)){e[a]=t[a]}}return e}},596:(e,t,a)=>{"use strict";
/*!
 * statuses
 * Copyright(c) 2014 Jonathan Ong
 * Copyright(c) 2016 Douglas Christopher Wilson
 * MIT Licensed
 */var i=a(64);e.exports=status;status.message=i;status.code=createMessageToStatusCodeMap(i);status.codes=createStatusCodeList(i);status.redirect={300:true,301:true,302:true,303:true,305:true,307:true,308:true};status.empty={204:true,205:true,304:true};status.retry={502:true,503:true,504:true};function createMessageToStatusCodeMap(e){var t={};Object.keys(e).forEach((function forEachCode(a){var i=e[a];var n=Number(a);t[i.toLowerCase()]=n}));return t}function createStatusCodeList(e){return Object.keys(e).map((function mapCode(e){return Number(e)}))}function getStatusCode(e){var t=e.toLowerCase();if(!Object.prototype.hasOwnProperty.call(status.code,t)){throw new Error('invalid status message: "'+e+'"')}return status.code[t]}function getStatusMessage(e){if(!Object.prototype.hasOwnProperty.call(status.message,e)){throw new Error("invalid status code: "+e)}return status.message[e]}function status(e){if(typeof e==="number"){return getStatusMessage(e)}if(typeof e!=="string"){throw new TypeError("code must be a number or string")}var t=parseInt(e,10);if(!isNaN(t)){return getStatusMessage(t)}return getStatusCode(e)}},637:e=>{"use strict";
/*!
 * toidentifier
 * Copyright(c) 2016 Douglas Christopher Wilson
 * MIT Licensed
 */e.exports=toIdentifier;function toIdentifier(e){return e.split(" ").map((function(e){return e.slice(0,1).toUpperCase()+e.slice(1)})).join("").replace(/[^ _0-9a-z]/gi,"")}},852:e=>{"use strict";e.exports=require("async_hooks")},113:e=>{"use strict";e.exports=require("crypto")},361:e=>{"use strict";e.exports=require("events")},147:e=>{"use strict";e.exports=require("fs")},937:e=>{"use strict";e.exports=require("next/dist/compiled/debug")},890:e=>{"use strict";e.exports=require("next/dist/compiled/fresh")},17:e=>{"use strict";e.exports=require("path")},781:e=>{"use strict";e.exports=require("stream")},837:e=>{"use strict";e.exports=require("util")},796:e=>{"use strict";e.exports=require("zlib")},598:e=>{"use strict";e.exports=JSON.parse('{"application/andrew-inset":["ez"],"application/applixware":["aw"],"application/atom+xml":["atom"],"application/atomcat+xml":["atomcat"],"application/atomsvc+xml":["atomsvc"],"application/bdoc":["bdoc"],"application/ccxml+xml":["ccxml"],"application/cdmi-capability":["cdmia"],"application/cdmi-container":["cdmic"],"application/cdmi-domain":["cdmid"],"application/cdmi-object":["cdmio"],"application/cdmi-queue":["cdmiq"],"application/cu-seeme":["cu"],"application/dash+xml":["mpd"],"application/davmount+xml":["davmount"],"application/docbook+xml":["dbk"],"application/dssc+der":["dssc"],"application/dssc+xml":["xdssc"],"application/ecmascript":["ecma"],"application/emma+xml":["emma"],"application/epub+zip":["epub"],"application/exi":["exi"],"application/font-tdpfr":["pfr"],"application/font-woff":[],"application/font-woff2":[],"application/geo+json":["geojson"],"application/gml+xml":["gml"],"application/gpx+xml":["gpx"],"application/gxf":["gxf"],"application/gzip":["gz"],"application/hyperstudio":["stk"],"application/inkml+xml":["ink","inkml"],"application/ipfix":["ipfix"],"application/java-archive":["jar","war","ear"],"application/java-serialized-object":["ser"],"application/java-vm":["class"],"application/javascript":["js","mjs"],"application/json":["json","map"],"application/json5":["json5"],"application/jsonml+json":["jsonml"],"application/ld+json":["jsonld"],"application/lost+xml":["lostxml"],"application/mac-binhex40":["hqx"],"application/mac-compactpro":["cpt"],"application/mads+xml":["mads"],"application/manifest+json":["webmanifest"],"application/marc":["mrc"],"application/marcxml+xml":["mrcx"],"application/mathematica":["ma","nb","mb"],"application/mathml+xml":["mathml"],"application/mbox":["mbox"],"application/mediaservercontrol+xml":["mscml"],"application/metalink+xml":["metalink"],"application/metalink4+xml":["meta4"],"application/mets+xml":["mets"],"application/mods+xml":["mods"],"application/mp21":["m21","mp21"],"application/mp4":["mp4s","m4p"],"application/msword":["doc","dot"],"application/mxf":["mxf"],"application/octet-stream":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"],"application/oda":["oda"],"application/oebps-package+xml":["opf"],"application/ogg":["ogx"],"application/omdoc+xml":["omdoc"],"application/onenote":["onetoc","onetoc2","onetmp","onepkg"],"application/oxps":["oxps"],"application/patch-ops-error+xml":["xer"],"application/pdf":["pdf"],"application/pgp-encrypted":["pgp"],"application/pgp-signature":["asc","sig"],"application/pics-rules":["prf"],"application/pkcs10":["p10"],"application/pkcs7-mime":["p7m","p7c"],"application/pkcs7-signature":["p7s"],"application/pkcs8":["p8"],"application/pkix-attr-cert":["ac"],"application/pkix-cert":["cer"],"application/pkix-crl":["crl"],"application/pkix-pkipath":["pkipath"],"application/pkixcmp":["pki"],"application/pls+xml":["pls"],"application/postscript":["ai","eps","ps"],"application/prs.cww":["cww"],"application/pskc+xml":["pskcxml"],"application/raml+yaml":["raml"],"application/rdf+xml":["rdf"],"application/reginfo+xml":["rif"],"application/relax-ng-compact-syntax":["rnc"],"application/resource-lists+xml":["rl"],"application/resource-lists-diff+xml":["rld"],"application/rls-services+xml":["rs"],"application/rpki-ghostbusters":["gbr"],"application/rpki-manifest":["mft"],"application/rpki-roa":["roa"],"application/rsd+xml":["rsd"],"application/rss+xml":["rss"],"application/rtf":["rtf"],"application/sbml+xml":["sbml"],"application/scvp-cv-request":["scq"],"application/scvp-cv-response":["scs"],"application/scvp-vp-request":["spq"],"application/scvp-vp-response":["spp"],"application/sdp":["sdp"],"application/set-payment-initiation":["setpay"],"application/set-registration-initiation":["setreg"],"application/shf+xml":["shf"],"application/smil+xml":["smi","smil"],"application/sparql-query":["rq"],"application/sparql-results+xml":["srx"],"application/srgs":["gram"],"application/srgs+xml":["grxml"],"application/sru+xml":["sru"],"application/ssdl+xml":["ssdl"],"application/ssml+xml":["ssml"],"application/tei+xml":["tei","teicorpus"],"application/thraud+xml":["tfi"],"application/timestamped-data":["tsd"],"application/vnd.3gpp.pic-bw-large":["plb"],"application/vnd.3gpp.pic-bw-small":["psb"],"application/vnd.3gpp.pic-bw-var":["pvb"],"application/vnd.3gpp2.tcap":["tcap"],"application/vnd.3m.post-it-notes":["pwn"],"application/vnd.accpac.simply.aso":["aso"],"application/vnd.accpac.simply.imp":["imp"],"application/vnd.acucobol":["acu"],"application/vnd.acucorp":["atc","acutc"],"application/vnd.adobe.air-application-installer-package+zip":["air"],"application/vnd.adobe.formscentral.fcdt":["fcdt"],"application/vnd.adobe.fxp":["fxp","fxpl"],"application/vnd.adobe.xdp+xml":["xdp"],"application/vnd.adobe.xfdf":["xfdf"],"application/vnd.ahead.space":["ahead"],"application/vnd.airzip.filesecure.azf":["azf"],"application/vnd.airzip.filesecure.azs":["azs"],"application/vnd.amazon.ebook":["azw"],"application/vnd.americandynamics.acc":["acc"],"application/vnd.amiga.ami":["ami"],"application/vnd.android.package-archive":["apk"],"application/vnd.anser-web-certificate-issue-initiation":["cii"],"application/vnd.anser-web-funds-transfer-initiation":["fti"],"application/vnd.antix.game-component":["atx"],"application/vnd.apple.installer+xml":["mpkg"],"application/vnd.apple.mpegurl":["m3u8"],"application/vnd.apple.pkpass":["pkpass"],"application/vnd.aristanetworks.swi":["swi"],"application/vnd.astraea-software.iota":["iota"],"application/vnd.audiograph":["aep"],"application/vnd.blueice.multipass":["mpm"],"application/vnd.bmi":["bmi"],"application/vnd.businessobjects":["rep"],"application/vnd.chemdraw+xml":["cdxml"],"application/vnd.chipnuts.karaoke-mmd":["mmd"],"application/vnd.cinderella":["cdy"],"application/vnd.claymore":["cla"],"application/vnd.cloanto.rp9":["rp9"],"application/vnd.clonk.c4group":["c4g","c4d","c4f","c4p","c4u"],"application/vnd.cluetrust.cartomobile-config":["c11amc"],"application/vnd.cluetrust.cartomobile-config-pkg":["c11amz"],"application/vnd.commonspace":["csp"],"application/vnd.contact.cmsg":["cdbcmsg"],"application/vnd.cosmocaller":["cmc"],"application/vnd.crick.clicker":["clkx"],"application/vnd.crick.clicker.keyboard":["clkk"],"application/vnd.crick.clicker.palette":["clkp"],"application/vnd.crick.clicker.template":["clkt"],"application/vnd.crick.clicker.wordbank":["clkw"],"application/vnd.criticaltools.wbs+xml":["wbs"],"application/vnd.ctc-posml":["pml"],"application/vnd.cups-ppd":["ppd"],"application/vnd.curl.car":["car"],"application/vnd.curl.pcurl":["pcurl"],"application/vnd.dart":["dart"],"application/vnd.data-vision.rdz":["rdz"],"application/vnd.dece.data":["uvf","uvvf","uvd","uvvd"],"application/vnd.dece.ttml+xml":["uvt","uvvt"],"application/vnd.dece.unspecified":["uvx","uvvx"],"application/vnd.dece.zip":["uvz","uvvz"],"application/vnd.denovo.fcselayout-link":["fe_launch"],"application/vnd.dna":["dna"],"application/vnd.dolby.mlp":["mlp"],"application/vnd.dpgraph":["dpg"],"application/vnd.dreamfactory":["dfac"],"application/vnd.ds-keypoint":["kpxx"],"application/vnd.dvb.ait":["ait"],"application/vnd.dvb.service":["svc"],"application/vnd.dynageo":["geo"],"application/vnd.ecowin.chart":["mag"],"application/vnd.enliven":["nml"],"application/vnd.epson.esf":["esf"],"application/vnd.epson.msf":["msf"],"application/vnd.epson.quickanime":["qam"],"application/vnd.epson.salt":["slt"],"application/vnd.epson.ssf":["ssf"],"application/vnd.eszigno3+xml":["es3","et3"],"application/vnd.ezpix-album":["ez2"],"application/vnd.ezpix-package":["ez3"],"application/vnd.fdf":["fdf"],"application/vnd.fdsn.mseed":["mseed"],"application/vnd.fdsn.seed":["seed","dataless"],"application/vnd.flographit":["gph"],"application/vnd.fluxtime.clip":["ftc"],"application/vnd.framemaker":["fm","frame","maker","book"],"application/vnd.frogans.fnc":["fnc"],"application/vnd.frogans.ltf":["ltf"],"application/vnd.fsc.weblaunch":["fsc"],"application/vnd.fujitsu.oasys":["oas"],"application/vnd.fujitsu.oasys2":["oa2"],"application/vnd.fujitsu.oasys3":["oa3"],"application/vnd.fujitsu.oasysgp":["fg5"],"application/vnd.fujitsu.oasysprs":["bh2"],"application/vnd.fujixerox.ddd":["ddd"],"application/vnd.fujixerox.docuworks":["xdw"],"application/vnd.fujixerox.docuworks.binder":["xbd"],"application/vnd.fuzzysheet":["fzs"],"application/vnd.genomatix.tuxedo":["txd"],"application/vnd.geogebra.file":["ggb"],"application/vnd.geogebra.tool":["ggt"],"application/vnd.geometry-explorer":["gex","gre"],"application/vnd.geonext":["gxt"],"application/vnd.geoplan":["g2w"],"application/vnd.geospace":["g3w"],"application/vnd.gmx":["gmx"],"application/vnd.google-apps.document":["gdoc"],"application/vnd.google-apps.presentation":["gslides"],"application/vnd.google-apps.spreadsheet":["gsheet"],"application/vnd.google-earth.kml+xml":["kml"],"application/vnd.google-earth.kmz":["kmz"],"application/vnd.grafeq":["gqf","gqs"],"application/vnd.groove-account":["gac"],"application/vnd.groove-help":["ghf"],"application/vnd.groove-identity-message":["gim"],"application/vnd.groove-injector":["grv"],"application/vnd.groove-tool-message":["gtm"],"application/vnd.groove-tool-template":["tpl"],"application/vnd.groove-vcard":["vcg"],"application/vnd.hal+xml":["hal"],"application/vnd.handheld-entertainment+xml":["zmm"],"application/vnd.hbci":["hbci"],"application/vnd.hhe.lesson-player":["les"],"application/vnd.hp-hpgl":["hpgl"],"application/vnd.hp-hpid":["hpid"],"application/vnd.hp-hps":["hps"],"application/vnd.hp-jlyt":["jlt"],"application/vnd.hp-pcl":["pcl"],"application/vnd.hp-pclxl":["pclxl"],"application/vnd.hydrostatix.sof-data":["sfd-hdstx"],"application/vnd.ibm.minipay":["mpy"],"application/vnd.ibm.modcap":["afp","listafp","list3820"],"application/vnd.ibm.rights-management":["irm"],"application/vnd.ibm.secure-container":["sc"],"application/vnd.iccprofile":["icc","icm"],"application/vnd.igloader":["igl"],"application/vnd.immervision-ivp":["ivp"],"application/vnd.immervision-ivu":["ivu"],"application/vnd.insors.igm":["igm"],"application/vnd.intercon.formnet":["xpw","xpx"],"application/vnd.intergeo":["i2g"],"application/vnd.intu.qbo":["qbo"],"application/vnd.intu.qfx":["qfx"],"application/vnd.ipunplugged.rcprofile":["rcprofile"],"application/vnd.irepository.package+xml":["irp"],"application/vnd.is-xpr":["xpr"],"application/vnd.isac.fcs":["fcs"],"application/vnd.jam":["jam"],"application/vnd.jcp.javame.midlet-rms":["rms"],"application/vnd.jisp":["jisp"],"application/vnd.joost.joda-archive":["joda"],"application/vnd.kahootz":["ktz","ktr"],"application/vnd.kde.karbon":["karbon"],"application/vnd.kde.kchart":["chrt"],"application/vnd.kde.kformula":["kfo"],"application/vnd.kde.kivio":["flw"],"application/vnd.kde.kontour":["kon"],"application/vnd.kde.kpresenter":["kpr","kpt"],"application/vnd.kde.kspread":["ksp"],"application/vnd.kde.kword":["kwd","kwt"],"application/vnd.kenameaapp":["htke"],"application/vnd.kidspiration":["kia"],"application/vnd.kinar":["kne","knp"],"application/vnd.koan":["skp","skd","skt","skm"],"application/vnd.kodak-descriptor":["sse"],"application/vnd.las.las+xml":["lasxml"],"application/vnd.llamagraphics.life-balance.desktop":["lbd"],"application/vnd.llamagraphics.life-balance.exchange+xml":["lbe"],"application/vnd.lotus-1-2-3":["123"],"application/vnd.lotus-approach":["apr"],"application/vnd.lotus-freelance":["pre"],"application/vnd.lotus-notes":["nsf"],"application/vnd.lotus-organizer":["org"],"application/vnd.lotus-screencam":["scm"],"application/vnd.lotus-wordpro":["lwp"],"application/vnd.macports.portpkg":["portpkg"],"application/vnd.mcd":["mcd"],"application/vnd.medcalcdata":["mc1"],"application/vnd.mediastation.cdkey":["cdkey"],"application/vnd.mfer":["mwf"],"application/vnd.mfmp":["mfm"],"application/vnd.micrografx.flo":["flo"],"application/vnd.micrografx.igx":["igx"],"application/vnd.mif":["mif"],"application/vnd.mobius.daf":["daf"],"application/vnd.mobius.dis":["dis"],"application/vnd.mobius.mbk":["mbk"],"application/vnd.mobius.mqy":["mqy"],"application/vnd.mobius.msl":["msl"],"application/vnd.mobius.plc":["plc"],"application/vnd.mobius.txf":["txf"],"application/vnd.mophun.application":["mpn"],"application/vnd.mophun.certificate":["mpc"],"application/vnd.mozilla.xul+xml":["xul"],"application/vnd.ms-artgalry":["cil"],"application/vnd.ms-cab-compressed":["cab"],"application/vnd.ms-excel":["xls","xlm","xla","xlc","xlt","xlw"],"application/vnd.ms-excel.addin.macroenabled.12":["xlam"],"application/vnd.ms-excel.sheet.binary.macroenabled.12":["xlsb"],"application/vnd.ms-excel.sheet.macroenabled.12":["xlsm"],"application/vnd.ms-excel.template.macroenabled.12":["xltm"],"application/vnd.ms-fontobject":["eot"],"application/vnd.ms-htmlhelp":["chm"],"application/vnd.ms-ims":["ims"],"application/vnd.ms-lrm":["lrm"],"application/vnd.ms-officetheme":["thmx"],"application/vnd.ms-outlook":["msg"],"application/vnd.ms-pki.seccat":["cat"],"application/vnd.ms-pki.stl":["stl"],"application/vnd.ms-powerpoint":["ppt","pps","pot"],"application/vnd.ms-powerpoint.addin.macroenabled.12":["ppam"],"application/vnd.ms-powerpoint.presentation.macroenabled.12":["pptm"],"application/vnd.ms-powerpoint.slide.macroenabled.12":["sldm"],"application/vnd.ms-powerpoint.slideshow.macroenabled.12":["ppsm"],"application/vnd.ms-powerpoint.template.macroenabled.12":["potm"],"application/vnd.ms-project":["mpp","mpt"],"application/vnd.ms-word.document.macroenabled.12":["docm"],"application/vnd.ms-word.template.macroenabled.12":["dotm"],"application/vnd.ms-works":["wps","wks","wcm","wdb"],"application/vnd.ms-wpl":["wpl"],"application/vnd.ms-xpsdocument":["xps"],"application/vnd.mseq":["mseq"],"application/vnd.musician":["mus"],"application/vnd.muvee.style":["msty"],"application/vnd.mynfc":["taglet"],"application/vnd.neurolanguage.nlu":["nlu"],"application/vnd.nitf":["ntf","nitf"],"application/vnd.noblenet-directory":["nnd"],"application/vnd.noblenet-sealer":["nns"],"application/vnd.noblenet-web":["nnw"],"application/vnd.nokia.n-gage.data":["ngdat"],"application/vnd.nokia.n-gage.symbian.install":["n-gage"],"application/vnd.nokia.radio-preset":["rpst"],"application/vnd.nokia.radio-presets":["rpss"],"application/vnd.novadigm.edm":["edm"],"application/vnd.novadigm.edx":["edx"],"application/vnd.novadigm.ext":["ext"],"application/vnd.oasis.opendocument.chart":["odc"],"application/vnd.oasis.opendocument.chart-template":["otc"],"application/vnd.oasis.opendocument.database":["odb"],"application/vnd.oasis.opendocument.formula":["odf"],"application/vnd.oasis.opendocument.formula-template":["odft"],"application/vnd.oasis.opendocument.graphics":["odg"],"application/vnd.oasis.opendocument.graphics-template":["otg"],"application/vnd.oasis.opendocument.image":["odi"],"application/vnd.oasis.opendocument.image-template":["oti"],"application/vnd.oasis.opendocument.presentation":["odp"],"application/vnd.oasis.opendocument.presentation-template":["otp"],"application/vnd.oasis.opendocument.spreadsheet":["ods"],"application/vnd.oasis.opendocument.spreadsheet-template":["ots"],"application/vnd.oasis.opendocument.text":["odt"],"application/vnd.oasis.opendocument.text-master":["odm"],"application/vnd.oasis.opendocument.text-template":["ott"],"application/vnd.oasis.opendocument.text-web":["oth"],"application/vnd.olpc-sugar":["xo"],"application/vnd.oma.dd2+xml":["dd2"],"application/vnd.openofficeorg.extension":["oxt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":["pptx"],"application/vnd.openxmlformats-officedocument.presentationml.slide":["sldx"],"application/vnd.openxmlformats-officedocument.presentationml.slideshow":["ppsx"],"application/vnd.openxmlformats-officedocument.presentationml.template":["potx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":["xlsx"],"application/vnd.openxmlformats-officedocument.spreadsheetml.template":["xltx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":["docx"],"application/vnd.openxmlformats-officedocument.wordprocessingml.template":["dotx"],"application/vnd.osgeo.mapguide.package":["mgp"],"application/vnd.osgi.dp":["dp"],"application/vnd.osgi.subsystem":["esa"],"application/vnd.palm":["pdb","pqa","oprc"],"application/vnd.pawaafile":["paw"],"application/vnd.pg.format":["str"],"application/vnd.pg.osasli":["ei6"],"application/vnd.picsel":["efif"],"application/vnd.pmi.widget":["wg"],"application/vnd.pocketlearn":["plf"],"application/vnd.powerbuilder6":["pbd"],"application/vnd.previewsystems.box":["box"],"application/vnd.proteus.magazine":["mgz"],"application/vnd.publishare-delta-tree":["qps"],"application/vnd.pvi.ptid1":["ptid"],"application/vnd.quark.quarkxpress":["qxd","qxt","qwd","qwt","qxl","qxb"],"application/vnd.realvnc.bed":["bed"],"application/vnd.recordare.musicxml":["mxl"],"application/vnd.recordare.musicxml+xml":["musicxml"],"application/vnd.rig.cryptonote":["cryptonote"],"application/vnd.rim.cod":["cod"],"application/vnd.rn-realmedia":["rm"],"application/vnd.rn-realmedia-vbr":["rmvb"],"application/vnd.route66.link66+xml":["link66"],"application/vnd.sailingtracker.track":["st"],"application/vnd.seemail":["see"],"application/vnd.sema":["sema"],"application/vnd.semd":["semd"],"application/vnd.semf":["semf"],"application/vnd.shana.informed.formdata":["ifm"],"application/vnd.shana.informed.formtemplate":["itp"],"application/vnd.shana.informed.interchange":["iif"],"application/vnd.shana.informed.package":["ipk"],"application/vnd.simtech-mindmapper":["twd","twds"],"application/vnd.smaf":["mmf"],"application/vnd.smart.teacher":["teacher"],"application/vnd.solent.sdkm+xml":["sdkm","sdkd"],"application/vnd.spotfire.dxp":["dxp"],"application/vnd.spotfire.sfs":["sfs"],"application/vnd.stardivision.calc":["sdc"],"application/vnd.stardivision.draw":["sda"],"application/vnd.stardivision.impress":["sdd"],"application/vnd.stardivision.math":["smf"],"application/vnd.stardivision.writer":["sdw","vor"],"application/vnd.stardivision.writer-global":["sgl"],"application/vnd.stepmania.package":["smzip"],"application/vnd.stepmania.stepchart":["sm"],"application/vnd.sun.wadl+xml":["wadl"],"application/vnd.sun.xml.calc":["sxc"],"application/vnd.sun.xml.calc.template":["stc"],"application/vnd.sun.xml.draw":["sxd"],"application/vnd.sun.xml.draw.template":["std"],"application/vnd.sun.xml.impress":["sxi"],"application/vnd.sun.xml.impress.template":["sti"],"application/vnd.sun.xml.math":["sxm"],"application/vnd.sun.xml.writer":["sxw"],"application/vnd.sun.xml.writer.global":["sxg"],"application/vnd.sun.xml.writer.template":["stw"],"application/vnd.sus-calendar":["sus","susp"],"application/vnd.svd":["svd"],"application/vnd.symbian.install":["sis","sisx"],"application/vnd.syncml+xml":["xsm"],"application/vnd.syncml.dm+wbxml":["bdm"],"application/vnd.syncml.dm+xml":["xdm"],"application/vnd.tao.intent-module-archive":["tao"],"application/vnd.tcpdump.pcap":["pcap","cap","dmp"],"application/vnd.tmobile-livetv":["tmo"],"application/vnd.trid.tpt":["tpt"],"application/vnd.triscape.mxs":["mxs"],"application/vnd.trueapp":["tra"],"application/vnd.ufdl":["ufd","ufdl"],"application/vnd.uiq.theme":["utz"],"application/vnd.umajin":["umj"],"application/vnd.unity":["unityweb"],"application/vnd.uoml+xml":["uoml"],"application/vnd.vcx":["vcx"],"application/vnd.visio":["vsd","vst","vss","vsw"],"application/vnd.visionary":["vis"],"application/vnd.vsf":["vsf"],"application/vnd.wap.wbxml":["wbxml"],"application/vnd.wap.wmlc":["wmlc"],"application/vnd.wap.wmlscriptc":["wmlsc"],"application/vnd.webturbo":["wtb"],"application/vnd.wolfram.player":["nbp"],"application/vnd.wordperfect":["wpd"],"application/vnd.wqd":["wqd"],"application/vnd.wt.stf":["stf"],"application/vnd.xara":["xar"],"application/vnd.xfdl":["xfdl"],"application/vnd.yamaha.hv-dic":["hvd"],"application/vnd.yamaha.hv-script":["hvs"],"application/vnd.yamaha.hv-voice":["hvp"],"application/vnd.yamaha.openscoreformat":["osf"],"application/vnd.yamaha.openscoreformat.osfpvg+xml":["osfpvg"],"application/vnd.yamaha.smaf-audio":["saf"],"application/vnd.yamaha.smaf-phrase":["spf"],"application/vnd.yellowriver-custom-menu":["cmp"],"application/vnd.zul":["zir","zirz"],"application/vnd.zzazz.deck+xml":["zaz"],"application/voicexml+xml":["vxml"],"application/wasm":["wasm"],"application/widget":["wgt"],"application/winhlp":["hlp"],"application/wsdl+xml":["wsdl"],"application/wspolicy+xml":["wspolicy"],"application/x-7z-compressed":["7z"],"application/x-abiword":["abw"],"application/x-ace-compressed":["ace"],"application/x-apple-diskimage":[],"application/x-arj":["arj"],"application/x-authorware-bin":["aab","x32","u32","vox"],"application/x-authorware-map":["aam"],"application/x-authorware-seg":["aas"],"application/x-bcpio":["bcpio"],"application/x-bdoc":[],"application/x-bittorrent":["torrent"],"application/x-blorb":["blb","blorb"],"application/x-bzip":["bz"],"application/x-bzip2":["bz2","boz"],"application/x-cbr":["cbr","cba","cbt","cbz","cb7"],"application/x-cdlink":["vcd"],"application/x-cfs-compressed":["cfs"],"application/x-chat":["chat"],"application/x-chess-pgn":["pgn"],"application/x-chrome-extension":["crx"],"application/x-cocoa":["cco"],"application/x-conference":["nsc"],"application/x-cpio":["cpio"],"application/x-csh":["csh"],"application/x-debian-package":["udeb"],"application/x-dgc-compressed":["dgc"],"application/x-director":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"],"application/x-doom":["wad"],"application/x-dtbncx+xml":["ncx"],"application/x-dtbook+xml":["dtb"],"application/x-dtbresource+xml":["res"],"application/x-dvi":["dvi"],"application/x-envoy":["evy"],"application/x-eva":["eva"],"application/x-font-bdf":["bdf"],"application/x-font-ghostscript":["gsf"],"application/x-font-linux-psf":["psf"],"application/x-font-pcf":["pcf"],"application/x-font-snf":["snf"],"application/x-font-type1":["pfa","pfb","pfm","afm"],"application/x-freearc":["arc"],"application/x-futuresplash":["spl"],"application/x-gca-compressed":["gca"],"application/x-glulx":["ulx"],"application/x-gnumeric":["gnumeric"],"application/x-gramps-xml":["gramps"],"application/x-gtar":["gtar"],"application/x-hdf":["hdf"],"application/x-httpd-php":["php"],"application/x-install-instructions":["install"],"application/x-iso9660-image":[],"application/x-java-archive-diff":["jardiff"],"application/x-java-jnlp-file":["jnlp"],"application/x-latex":["latex"],"application/x-lua-bytecode":["luac"],"application/x-lzh-compressed":["lzh","lha"],"application/x-makeself":["run"],"application/x-mie":["mie"],"application/x-mobipocket-ebook":["prc","mobi"],"application/x-ms-application":["application"],"application/x-ms-shortcut":["lnk"],"application/x-ms-wmd":["wmd"],"application/x-ms-wmz":["wmz"],"application/x-ms-xbap":["xbap"],"application/x-msaccess":["mdb"],"application/x-msbinder":["obd"],"application/x-mscardfile":["crd"],"application/x-msclip":["clp"],"application/x-msdos-program":[],"application/x-msdownload":["com","bat"],"application/x-msmediaview":["mvb","m13","m14"],"application/x-msmetafile":["wmf","emf","emz"],"application/x-msmoney":["mny"],"application/x-mspublisher":["pub"],"application/x-msschedule":["scd"],"application/x-msterminal":["trm"],"application/x-mswrite":["wri"],"application/x-netcdf":["nc","cdf"],"application/x-ns-proxy-autoconfig":["pac"],"application/x-nzb":["nzb"],"application/x-perl":["pl","pm"],"application/x-pilot":[],"application/x-pkcs12":["p12","pfx"],"application/x-pkcs7-certificates":["p7b","spc"],"application/x-pkcs7-certreqresp":["p7r"],"application/x-rar-compressed":["rar"],"application/x-redhat-package-manager":["rpm"],"application/x-research-info-systems":["ris"],"application/x-sea":["sea"],"application/x-sh":["sh"],"application/x-shar":["shar"],"application/x-shockwave-flash":["swf"],"application/x-silverlight-app":["xap"],"application/x-sql":["sql"],"application/x-stuffit":["sit"],"application/x-stuffitx":["sitx"],"application/x-subrip":["srt"],"application/x-sv4cpio":["sv4cpio"],"application/x-sv4crc":["sv4crc"],"application/x-t3vm-image":["t3"],"application/x-tads":["gam"],"application/x-tar":["tar"],"application/x-tcl":["tcl","tk"],"application/x-tex":["tex"],"application/x-tex-tfm":["tfm"],"application/x-texinfo":["texinfo","texi"],"application/x-tgif":["obj"],"application/x-ustar":["ustar"],"application/x-virtualbox-hdd":["hdd"],"application/x-virtualbox-ova":["ova"],"application/x-virtualbox-ovf":["ovf"],"application/x-virtualbox-vbox":["vbox"],"application/x-virtualbox-vbox-extpack":["vbox-extpack"],"application/x-virtualbox-vdi":["vdi"],"application/x-virtualbox-vhd":["vhd"],"application/x-virtualbox-vmdk":["vmdk"],"application/x-wais-source":["src"],"application/x-web-app-manifest+json":["webapp"],"application/x-x509-ca-cert":["der","crt","pem"],"application/x-xfig":["fig"],"application/x-xliff+xml":["xlf"],"application/x-xpinstall":["xpi"],"application/x-xz":["xz"],"application/x-zmachine":["z1","z2","z3","z4","z5","z6","z7","z8"],"application/xaml+xml":["xaml"],"application/xcap-diff+xml":["xdf"],"application/xenc+xml":["xenc"],"application/xhtml+xml":["xhtml","xht"],"application/xml":["xml","xsl","xsd","rng"],"application/xml-dtd":["dtd"],"application/xop+xml":["xop"],"application/xproc+xml":["xpl"],"application/xslt+xml":["xslt"],"application/xspf+xml":["xspf"],"application/xv+xml":["mxml","xhvml","xvml","xvm"],"application/yang":["yang"],"application/yin+xml":["yin"],"application/zip":["zip"],"audio/3gpp":[],"audio/adpcm":["adp"],"audio/basic":["au","snd"],"audio/midi":["mid","midi","kar","rmi"],"audio/mp3":[],"audio/mp4":["m4a","mp4a"],"audio/mpeg":["mpga","mp2","mp2a","mp3","m2a","m3a"],"audio/ogg":["oga","ogg","spx"],"audio/s3m":["s3m"],"audio/silk":["sil"],"audio/vnd.dece.audio":["uva","uvva"],"audio/vnd.digital-winds":["eol"],"audio/vnd.dra":["dra"],"audio/vnd.dts":["dts"],"audio/vnd.dts.hd":["dtshd"],"audio/vnd.lucent.voice":["lvp"],"audio/vnd.ms-playready.media.pya":["pya"],"audio/vnd.nuera.ecelp4800":["ecelp4800"],"audio/vnd.nuera.ecelp7470":["ecelp7470"],"audio/vnd.nuera.ecelp9600":["ecelp9600"],"audio/vnd.rip":["rip"],"audio/wav":["wav"],"audio/wave":[],"audio/webm":["weba"],"audio/x-aac":["aac"],"audio/x-aiff":["aif","aiff","aifc"],"audio/x-caf":["caf"],"audio/x-flac":["flac"],"audio/x-m4a":[],"audio/x-matroska":["mka"],"audio/x-mpegurl":["m3u"],"audio/x-ms-wax":["wax"],"audio/x-ms-wma":["wma"],"audio/x-pn-realaudio":["ram","ra"],"audio/x-pn-realaudio-plugin":["rmp"],"audio/x-realaudio":[],"audio/x-wav":[],"audio/xm":["xm"],"chemical/x-cdx":["cdx"],"chemical/x-cif":["cif"],"chemical/x-cmdf":["cmdf"],"chemical/x-cml":["cml"],"chemical/x-csml":["csml"],"chemical/x-xyz":["xyz"],"font/collection":["ttc"],"font/otf":["otf"],"font/ttf":["ttf"],"font/woff":["woff"],"font/woff2":["woff2"],"image/apng":["apng"],"image/bmp":["bmp"],"image/cgm":["cgm"],"image/g3fax":["g3"],"image/gif":["gif"],"image/ief":["ief"],"image/jp2":["jp2","jpg2"],"image/jpeg":["jpeg","jpg","jpe"],"image/jpm":["jpm"],"image/jpx":["jpx","jpf"],"image/ktx":["ktx"],"image/png":["png"],"image/prs.btif":["btif"],"image/sgi":["sgi"],"image/svg+xml":["svg","svgz"],"image/tiff":["tiff","tif"],"image/vnd.adobe.photoshop":["psd"],"image/vnd.dece.graphic":["uvi","uvvi","uvg","uvvg"],"image/vnd.djvu":["djvu","djv"],"image/vnd.dvb.subtitle":[],"image/vnd.dwg":["dwg"],"image/vnd.dxf":["dxf"],"image/vnd.fastbidsheet":["fbs"],"image/vnd.fpx":["fpx"],"image/vnd.fst":["fst"],"image/vnd.fujixerox.edmics-mmr":["mmr"],"image/vnd.fujixerox.edmics-rlc":["rlc"],"image/vnd.ms-modi":["mdi"],"image/vnd.ms-photo":["wdp"],"image/vnd.net-fpx":["npx"],"image/vnd.wap.wbmp":["wbmp"],"image/vnd.xiff":["xif"],"image/webp":["webp"],"image/x-3ds":["3ds"],"image/x-cmu-raster":["ras"],"image/x-cmx":["cmx"],"image/x-freehand":["fh","fhc","fh4","fh5","fh7"],"image/x-icon":["ico"],"image/x-jng":["jng"],"image/x-mrsid-image":["sid"],"image/x-ms-bmp":[],"image/x-pcx":["pcx"],"image/x-pict":["pic","pct"],"image/x-portable-anymap":["pnm"],"image/x-portable-bitmap":["pbm"],"image/x-portable-graymap":["pgm"],"image/x-portable-pixmap":["ppm"],"image/x-rgb":["rgb"],"image/x-tga":["tga"],"image/x-xbitmap":["xbm"],"image/x-xpixmap":["xpm"],"image/x-xwindowdump":["xwd"],"message/rfc822":["eml","mime"],"model/gltf+json":["gltf"],"model/gltf-binary":["glb"],"model/iges":["igs","iges"],"model/mesh":["msh","mesh","silo"],"model/vnd.collada+xml":["dae"],"model/vnd.dwf":["dwf"],"model/vnd.gdl":["gdl"],"model/vnd.gtw":["gtw"],"model/vnd.mts":["mts"],"model/vnd.vtu":["vtu"],"model/vrml":["wrl","vrml"],"model/x3d+binary":["x3db","x3dbz"],"model/x3d+vrml":["x3dv","x3dvz"],"model/x3d+xml":["x3d","x3dz"],"text/cache-manifest":["appcache","manifest"],"text/calendar":["ics","ifb"],"text/coffeescript":["coffee","litcoffee"],"text/css":["css"],"text/csv":["csv"],"text/hjson":["hjson"],"text/html":["html","htm","shtml"],"text/jade":["jade"],"text/jsx":["jsx"],"text/less":["less"],"text/markdown":["markdown","md"],"text/mathml":["mml"],"text/n3":["n3"],"text/plain":["txt","text","conf","def","list","log","in","ini"],"text/prs.lines.tag":["dsc"],"text/richtext":["rtx"],"text/rtf":[],"text/sgml":["sgml","sgm"],"text/slim":["slim","slm"],"text/stylus":["stylus","styl"],"text/tab-separated-values":["tsv"],"text/troff":["t","tr","roff","man","me","ms"],"text/turtle":["ttl"],"text/uri-list":["uri","uris","urls"],"text/vcard":["vcard"],"text/vnd.curl":["curl"],"text/vnd.curl.dcurl":["dcurl"],"text/vnd.curl.mcurl":["mcurl"],"text/vnd.curl.scurl":["scurl"],"text/vnd.dvb.subtitle":["sub"],"text/vnd.fly":["fly"],"text/vnd.fmi.flexstor":["flx"],"text/vnd.graphviz":["gv"],"text/vnd.in3d.3dml":["3dml"],"text/vnd.in3d.spot":["spot"],"text/vnd.sun.j2me.app-descriptor":["jad"],"text/vnd.wap.wml":["wml"],"text/vnd.wap.wmlscript":["wmls"],"text/vtt":["vtt"],"text/x-asm":["s","asm"],"text/x-c":["c","cc","cxx","cpp","h","hh","dic"],"text/x-component":["htc"],"text/x-fortran":["f","for","f77","f90"],"text/x-handlebars-template":["hbs"],"text/x-java-source":["java"],"text/x-lua":["lua"],"text/x-markdown":["mkd"],"text/x-nfo":["nfo"],"text/x-opml":["opml"],"text/x-org":[],"text/x-pascal":["p","pas"],"text/x-processing":["pde"],"text/x-sass":["sass"],"text/x-scss":["scss"],"text/x-setext":["etx"],"text/x-sfv":["sfv"],"text/x-suse-ymp":["ymp"],"text/x-uuencode":["uu"],"text/x-vcalendar":["vcs"],"text/x-vcard":["vcf"],"text/xml":[],"text/yaml":["yaml","yml"],"video/3gpp":["3gp","3gpp"],"video/3gpp2":["3g2"],"video/h261":["h261"],"video/h263":["h263"],"video/h264":["h264"],"video/jpeg":["jpgv"],"video/jpm":["jpgm"],"video/mj2":["mj2","mjp2"],"video/mp2t":["ts"],"video/mp4":["mp4","mp4v","mpg4"],"video/mpeg":["mpeg","mpg","mpe","m1v","m2v"],"video/ogg":["ogv"],"video/quicktime":["qt","mov"],"video/vnd.dece.hd":["uvh","uvvh"],"video/vnd.dece.mobile":["uvm","uvvm"],"video/vnd.dece.pd":["uvp","uvvp"],"video/vnd.dece.sd":["uvs","uvvs"],"video/vnd.dece.video":["uvv","uvvv"],"video/vnd.dvb.file":["dvb"],"video/vnd.fvt":["fvt"],"video/vnd.mpegurl":["mxu","m4u"],"video/vnd.ms-playready.media.pyv":["pyv"],"video/vnd.uvvu.mp4":["uvu","uvvu"],"video/vnd.vivo":["viv"],"video/webm":["webm"],"video/x-f4v":["f4v"],"video/x-fli":["fli"],"video/x-flv":["flv"],"video/x-m4v":["m4v"],"video/x-matroska":["mkv","mk3d","mks"],"video/x-mng":["mng"],"video/x-ms-asf":["asf","asx"],"video/x-ms-vob":["vob"],"video/x-ms-wm":["wm"],"video/x-ms-wmv":["wmv"],"video/x-ms-wmx":["wmx"],"video/x-ms-wvx":["wvx"],"video/x-msvideo":["avi"],"video/x-sgi-movie":["movie"],"video/x-smv":["smv"],"x-conference/x-cooltalk":["ice"]}')},64:e=>{"use strict";e.exports=JSON.parse('{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I\'m a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"}')}};var t={};function __nccwpck_require__(a){var i=t[a];if(i!==undefined){return i.exports}var n=t[a]={exports:{}};var r=true;try{e[a](n,n.exports,__nccwpck_require__);r=false}finally{if(r)delete t[a]}return n.exports}if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var a=__nccwpck_require__(228);module.exports=a})();