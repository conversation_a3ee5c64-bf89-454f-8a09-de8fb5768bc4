[{"/Users/<USER>/Desktop/emily_ai/frontend/src/app/auth/login/page.tsx": "1", "/Users/<USER>/Desktop/emily_ai/frontend/src/app/dashboard/page.tsx": "2", "/Users/<USER>/Desktop/emily_ai/frontend/src/app/layout.tsx": "3", "/Users/<USER>/Desktop/emily_ai/frontend/src/app/page.tsx": "4", "/Users/<USER>/Desktop/emily_ai/frontend/src/components/ProtectedRoute.tsx": "5", "/Users/<USER>/Desktop/emily_ai/frontend/src/contexts/AuthContext.tsx": "6", "/Users/<USER>/Desktop/emily_ai/frontend/src/lib/api.ts": "7", "/Users/<USER>/Desktop/emily_ai/frontend/src/lib/config.ts": "8", "/Users/<USER>/Desktop/emily_ai/frontend/src/services/auth.ts": "9", "/Users/<USER>/Desktop/emily_ai/frontend/src/types/auth.ts": "10"}, {"size": 9472, "mtime": 1757428620616, "results": "11", "hashOfConfig": "12"}, {"size": 9506, "mtime": 1757428620616, "results": "13", "hashOfConfig": "12"}, {"size": 1157, "mtime": 1757427692772, "results": "14", "hashOfConfig": "12"}, {"size": 7927, "mtime": 1757428620619, "results": "15", "hashOfConfig": "12"}, {"size": 1350, "mtime": 1757426939183, "results": "16", "hashOfConfig": "12"}, {"size": 5035, "mtime": 1757427692772, "results": "17", "hashOfConfig": "12"}, {"size": 1331, "mtime": 1757426939165, "results": "18", "hashOfConfig": "12"}, {"size": 495, "mtime": 1757426939163, "results": "19", "hashOfConfig": "12"}, {"size": 1750, "mtime": 1757606178217, "results": "20", "hashOfConfig": "12"}, {"size": 898, "mtime": 1757426939164, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "gqe60z", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/emily_ai/frontend/src/app/auth/login/page.tsx", [], [], "/Users/<USER>/Desktop/emily_ai/frontend/src/app/dashboard/page.tsx", [], [], "/Users/<USER>/Desktop/emily_ai/frontend/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/emily_ai/frontend/src/app/page.tsx", ["52"], [], "/Users/<USER>/Desktop/emily_ai/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/emily_ai/frontend/src/contexts/AuthContext.tsx", ["53"], [], "/Users/<USER>/Desktop/emily_ai/frontend/src/lib/api.ts", [], [], "/Users/<USER>/Desktop/emily_ai/frontend/src/lib/config.ts", [], [], "/Users/<USER>/Desktop/emily_ai/frontend/src/services/auth.ts", [], [], "/Users/<USER>/Desktop/emily_ai/frontend/src/types/auth.ts", [], [], {"ruleId": "54", "severity": 1, "message": "55", "line": 12, "column": 3, "nodeType": null, "messageId": "56", "endLine": 12, "endColumn": 8}, {"ruleId": "54", "severity": 1, "message": "57", "line": 45, "column": 20, "nodeType": null, "messageId": "56", "endLine": 45, "endColumn": 25}, "@typescript-eslint/no-unused-vars", "'Clock' is defined but never used.", "unusedVar", "'error' is defined but never used."]